<?php

declare(strict_types=1);

namespace LoginAutonom\ExternalResourcesBundle\Guesser;

use Carbon\Carbon;
use LoginAutonom\CoreBundle\Builder\DateRangeConfigDTOBuilder;
use LoginAutonom\CoreBundle\DTO\DateRangeConfigDTO;
use LoginAutonom\CoreBundle\DTO\ValidityInterval;
use LoginAutonom\CoreBundle\Enum\DateRangeConfigKeyEnum;
use LoginAutonom\CoreBundle\Guesser\DateRangeGuesser;
use LoginAutonom\ExternalResourcesBundle\DTO\RequestParameterContextDTO;
use LoginAutonom\ExternalResourcesBundle\Enum\ApiExtractorConfigurationKeyEnum;

final readonly class DateParamGuesser
{
    public function __construct(
        private DateRangeGuesser $dateRangeGuesser,
        private DateRangeConfigDTOBuilder $dateRangeConfigDTOBuilder,
    ) {
    }

    public function guess(RequestParameterContextDTO $context): ValidityInterval
    {
        if (!$context->hasRequestConfig()) {
            return [];
        }

        $requestConfig = $context->getRequestConfig();
        $dateParameters = [];

        if (isset($requestConfig[DateRangeConfigKeyEnum::DATE_PATTERN->value])) {
            $dateRangeConfigDTO = $this->buildDateRangeConfig($requestConfig);
            $datePattern = $this->dateRangeGuesser->guess($dateRangeConfigDTO);

            return [
                ApiExtractorConfigurationKeyEnum::FROM->value => $datePattern[DateRangeConfigKeyEnum::FROM->value],
                ApiExtractorConfigurationKeyEnum::TO->value => $datePattern[DateRangeConfigKeyEnum::TO->value],
            ];
        }

        if (
            isset($requestConfig[ApiExtractorConfigurationKeyEnum::FROM->value]) &&
            isset($requestConfig[ApiExtractorConfigurationKeyEnum::TO->value])
        ) {
            $dateParameters = [
                ApiExtractorConfigurationKeyEnum::FROM->value => $requestConfig[ApiExtractorConfigurationKeyEnum::FROM->value],
                ApiExtractorConfigurationKeyEnum::TO->value => $requestConfig[ApiExtractorConfigurationKeyEnum::TO->value],
            ];
        }

        return $dateParameters;
    }

    private function buildDateRangeConfig(array $requestConfig): DateRangeConfigDTO
    {
        return
            $this->dateRangeConfigDTOBuilder->reset()
                ->setDatePattern($requestConfig[DateRangeConfigKeyEnum::DATE_PATTERN->value])
                ->setBaseDate(Carbon::now())
                ->setCustomFrom($requestConfig[DateRangeConfigKeyEnum::CUSTOM_FROM->value] ?? null)
                ->setCustomTo($requestConfig[DateRangeConfigKeyEnum::CUSTOM_TO->value] ?? null)
                ->build();
    }
}
