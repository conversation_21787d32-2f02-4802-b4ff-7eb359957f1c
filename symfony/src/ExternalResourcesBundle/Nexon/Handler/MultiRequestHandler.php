<?php

declare(strict_types=1);

namespace LoginAutonom\ExternalResourcesBundle\Nexon\Handler;

use LoginAutonom\CoreBundle\Iterator\ExtendByIndexedArrayIterator;
use LoginAutonom\CoreBundle\Util\ArrayUtil;
use LoginAutonom\ExternalResourcesBundle\Builder\RequestParametersDTOBuilder;
use LoginAutonom\ExternalResourcesBundle\DTO\ApiConfigurationDTO;
use LoginAutonom\ExternalResourcesBundle\DTO\APIDynamicParamsDTO;
use LoginAutonom\ExternalResourcesBundle\DTO\ProcessingContextDTO;
use LoginAutonom\ExternalResourcesBundle\DTO\RequestParameterContextDTO;
use LoginAutonom\ExternalResourcesBundle\Enum\ApiExtractorConfigurationKeyEnum;
use LoginAutonom\ExternalResourcesBundle\Enum\ApiRequestConfigurationKeyEnum;
use LoginAutonom\ExternalResourcesBundle\Guesser\DateParamGuesser;
use LoginAutonom\ExternalResourcesBundle\Guesser\DynamicParamsGuesser;
use LoginAutonom\ExternalResourcesBundle\Guesser\RequestConfigParamsGuesser;
use LoginAutonom\ExternalResourcesBundle\Guesser\RequestSpecificParamsGuesser;
use LoginAutonom\ExternalResourcesBundle\Interfaces\RequestHandlerInterface;
use LoginAutonom\ExternalResourcesBundle\Nexon\Enum\ApiResponseValueEnum;
use LoginAutonom\ExternalResourcesBundle\Nexon\Util\FieldMappingUtil;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;
use Symfony\Component\DependencyInjection\Attribute\Autoconfigure;
use Symfony\Component\DependencyInjection\Attribute\TaggedLocator;
use Symfony\Contracts\Service\ServiceProviderInterface;

#[Autoconfigure]
final class MultiRequestHandler implements LoggerAwareInterface
{
    use LoggerAwareTrait;

    public function __construct(
        #[TaggedLocator(RequestHandlerInterface::TAG, defaultIndexMethod: 'type')]
        private readonly ServiceProviderInterface $requests,
        private readonly RequestParametersDTOBuilder $parameterBuilder,
        private readonly DateParamGuesser $dateParamGuesser,
        private readonly RequestSpecificParamsGuesser $requestSpecificParamsGuesser,
        private readonly RequestConfigParamsGuesser $requestConfigParamsGuesser,
        private readonly DynamicParamsGuesser $dynamicParamsGuesser,
        private readonly FieldMappingUtil $fieldMappingUtil,
        private readonly APIDynamicParamsDTO $dynamicParamsDTO,
    ) {
    }

    /**
     * @throws \Exception
     */
    public function handle(
        array $requests,
        array $options,
        ApiConfigurationDTO $apiConfiguration
    ): array {
        $results = [];
        $requests = $this->sortRequests($requests);
        foreach ($requests as $requestConfig) {
            $requestType = $requestConfig[ApiRequestConfigurationKeyEnum::TYPE->value] ?? $requestConfig;
            if (!$this->requests->has($requestType)) {
                $this->logger->warning('Request handler not found for type: {type}', ['type' => $requestType]);
                continue;
            }

            if (isset($results[$requestType])) {
                continue;
            }

            try {
                $processingContext = new ProcessingContextDTO(
                    $requestType,
                    $requestConfig,
                    $options,
                    $apiConfiguration
                );

                $processedResults = $this->processRequest($processingContext);

                $parent = $requestConfig[ApiRequestConfigurationKeyEnum::PARENT->value] ?? $requestType;
                $iterator = new ExtendByIndexedArrayIterator(
                    new \ArrayIterator($processedResults),
                    $results,
                    $parent
                );
                $processedResults = iterator_to_array($iterator);
                $results = $processedResults;
            } catch (\Exception $e) {
                $this->logger->error('Error processing request type {type}: {message}', [
                    'type' => $requestType,
                    'message' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                ]);
                throw $e;
            }
        }

        return $results;
    }

    private function processRequest(ProcessingContextDTO $context): array
    {
        /** @var RequestHandlerInterface $request */
        $request = $this->requests->get($context->getRequestType());

        $parametersContext = new RequestParameterContextDTO(
            $context->getOptions(),
            $context->getRequestType(),
            $context->getRequestConfig()
        );

        $requestParams = $this->getRequestParams($parametersContext);
        $requestRawData = $request->handle($context->getApiConfiguration(), $requestParams);
        $processedData = $this->processResponseData($requestRawData);
        $processedData = $this->renameMapping($processedData, $context);
        $this->extractDynamicParams($parametersContext, $processedData);

        if (isset($context->getRequestConfig()[ApiRequestConfigurationKeyEnum::GROUP_BY->value])) {
            $groupByKey = $context->getRequestConfig()[ApiRequestConfigurationKeyEnum::GROUP_BY->value];
            $processedData = ArrayUtil::groupBy(
                $processedData,
                fn($item) => $item[$groupByKey] ?? null
            );
        }

        return $processedData;
    }

    private function processResponseData(mixed $rawData): array
    {
        if (empty($rawData)) {
            return [];
        }

        $data = [];
        if (is_string($rawData)) {
            $data = json_decode($rawData, true) ?? [];
        } elseif (is_array($rawData)) {
            $data = $rawData;
        }

        return $data[ApiResponseValueEnum::VALUE->value] ?? $data;
    }

    private function getRequestParams(RequestParameterContextDTO $context): array
    {
        return $this->parameterBuilder
            ->reset()
            ->setDateParameters(
                $this->dateParamGuesser->guess($context)
            )
            ->setRequestSpecificParameters(
                $this->requestSpecificParamsGuesser->guess($context)
            )
            ->setRequestConfigParameters(
                $this->requestConfigParamsGuesser->guess($context)
            )
            ->setDynamicParameters(
                $this->dynamicParamsGuesser->guess(
                    $context,
                    $this->dynamicParamsDTO
                )
            )
            ->build()
            ->toArray();
    }

    private function sortRequests(array $requests): array
    {
        return ArrayUtil::sortByKey($requests, ApiRequestConfigurationKeyEnum::ORDER->value, 'ASC');
    }

    private function renameMapping(array $processedData, ProcessingContextDTO $context): array
    {
        $requestType = $context->getRequestType();
        $options = $context->getOptions();

        if (isset($options[ApiExtractorConfigurationKeyEnum::FIELD_MAPPINGS->value][$requestType])) {
            $mappings = $options[ApiExtractorConfigurationKeyEnum::FIELD_MAPPINGS->value][$requestType];
            return $this->fieldMappingUtil->applyFieldMappings($processedData, $mappings);
        }

        return $processedData;
    }

    private function extractDynamicParams(RequestParameterContextDTO $parametersContext, array $processedData): void
    {
        $requestType = $parametersContext->getRequestType();
        $requestConfig = $parametersContext->getRequestConfig();

        if ($this->dynamicParamsDTO->hasDynamicParamsByRequestType($requestType)) {
            return;
        }

        $fieldsToExtract = $requestConfig[ApiRequestConfigurationKeyEnum::PARAMS_TO_EXTRACT->value] ?? [];
        if (empty($fieldsToExtract)) {
            return;
        }

        $newDynamicParams = $this->collectDynamicParams($fieldsToExtract, $processedData);

        if (empty($newDynamicParams)) {
            return;
        }

        $this->dynamicParamsDTO->addDynamicParamsByType($requestType, $newDynamicParams);
    }

    private function collectDynamicParams(array $fields, array $processedData): array
    {
        $uniqueFields = array_unique($fields);
        $dynamicParams = [];

        foreach ($uniqueFields as $field) {
            $values = ArrayUtil::collectUniqueFieldValues($processedData, $field);
            if (empty($values)) {
                continue;
            }
            $dynamicParams[$field] = array_values($values);
        }

        return $dynamicParams;
    }
}
