<?php

declare(strict_types=1);

namespace LoginAutonom\EtlBundle\Flow\Transformer;

use Flow\ETL\FlowContext;
use Flow\ETL\Row;
use LoginAutonom\CoreBundle\DTO\SingleValidity;
use LoginAutonom\CoreBundle\DTO\ValidityInterval;
use LoginAutonom\CoreBundle\Exception\NotFoundException;
use LoginAutonom\DatabaseBundle\Builder\EntityChangePlanBuilder;
use LoginAutonom\DatabaseBundle\Messenger\Stamp\NoAutoJoinToEmployee;
use LoginAutonom\DatabaseBundle\Messenger\Stamp\NoAutoJoinToEmployeeContract;
use LoginAutonom\DatabaseBundle\Messenger\Stamp\NoVisibilityPermissionStamp;
use LoginAutonom\DatabaseBundle\Messenger\Stamp\ValidityIntervalStamp;
use LoginAutonom\DatabaseBundle\Provider\MultiEntitiesByIdentifiersProvider;
use LoginAutonom\EtlBundle\DTO\EtlEntityModifyInfo;
use LoginAutonom\EtlBundle\Interfaces\EtlCacheAwareInterface;
use LoginAutonom\EtlBundle\Interfaces\EtlEntityModifyHandlerInterface;
use LoginAutonom\EtlBundle\Interfaces\FlowOneRowTransformerInterface;
use LoginAutonom\EtlBundle\Trait\EtlCacheAwareTrait;
use LoginAutonom\EtlBundle\Trait\EtlCommonFunctionsTrait;
use LoginAutonom\EtlBundle\Trait\FlowTransformerTrait;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;

final class UpdateValidityBasedEntityTransformer implements
    FlowOneRowTransformerInterface,
    EtlCacheAwareInterface,
    LoggerAwareInterface
{
    use EtlCacheAwareTrait;
    use EtlCommonFunctionsTrait;
    use FlowTransformerTrait;
    use LoggerAwareTrait;

    public function __construct(
        private readonly array $fieldMap,
        private readonly string $fromField,
        private readonly array $identifierFields,
        private readonly string $entityClass,
        private readonly EntityChangePlanBuilder $changePlanBuilder,
        private readonly EtlEntityModifyHandlerInterface $modifyHandler,
        private readonly MultiEntitiesByIdentifiersProvider $entitiesByIdentifiersProvider,
        private readonly ?string $toField = null,
    ) {
    }

    public function transform(Row $row, FlowContext $context): Row
    {
        $this->logger->debug('Finding entities for update by identifiers');
        $entitiesInValidityPeriod = $this->getEntitiesByIdentifiersInValidityPeriod($row, $context);

        if (empty($entitiesInValidityPeriod)) {
            $this->logger->debug('No entities found in validity period, skipping update');
            return $row;
        }

        $entityCache = $this->getEntityCache($context);
        $changesStorage = $this->getChangesStorage($row);

        foreach ($entitiesInValidityPeriod as $originalEntity) {
            $this->logger->debug('Create change plan for update');
            $changePlan = $this->changePlanBuilder->reset()
                ->setEntity($originalEntity)
                ->setFieldMap($this->fieldMap)
                ->setFields($this->getValuesFromRow($row))
                ->build();

            if (!$changePlan->hasChanges()) {
                $this->logger->debug('No changes detected for entity, skipping');
                continue;
            }

            if (isset($this->toField)) {
                $this->logger->debug('Creating ValidityInterval for update');
                $validity = new ValidityInterval(
                    $row->get($this->fromField)->value(),
                    $row->get($this->toField)->value()
                );
            } else {
                $this->logger->debug('Creating SingleValidity for update');
                $validity = new SingleValidity(
                    $row->get($this->fromField)->value()
                );
            }

            $this->logger->debug('Execute update via modifyHandler');
            $entityChanges = $this->modifyHandler->modify(
                new EtlEntityModifyInfo(
                    $originalEntity,
                    $changePlan,
                    $entityCache,
                    $validity
                )
            );

            $this->logger->debug('Add update changes to storage');
            $changesStorage->mergeFrom($entityChanges->getChanges());
            $finalEntity = $entityChanges->getFinalEntity();
            $changesStorage->addChanged($finalEntity);
        }
        
        return $row;
    }

    /**
     * @throws \Flow\ETL\Exception\InvalidArgumentException
     * @throws \Exception
     */
    private function getEntitiesByIdentifiersInValidityPeriod(Row $row, FlowContext $context): array
    {
        $identifiers = $this->collectIdentifiersFromRow($row);
        if (empty($identifiers)) {
            $this->logger->debug('No identifiers found in row');
            return [];
        }

        $entityCache = $this->getEntityCache($context);

        try {
            $validityIntervalBasedEntity = $entityCache->get($identifiers, $this->entityClass);
            return $this->filterEntitiesByValidityPeriod($validityIntervalBasedEntity, $row);
        } catch (NotFoundException $e) {
        }

        $stamps = [
            new NoVisibilityPermissionStamp('ETL Update'),
            new NoAutoJoinToEmployee(),
            new NoAutoJoinToEmployeeContract()
        ];

        if (!isset($this->fromField) || !$row->has($this->fromField)) {
            throw new \InvalidArgumentException('fromField not exists');
        }

        $stamps[] = new ValidityIntervalStamp(
            $row->get($this->fromField)->value(),
            $row->get($this->toField)->value()
        );

        $entities = $this->entitiesByIdentifiersProvider->provide(
            $this->entityClass,
            [$identifiers],
            $stamps
        );

        foreach ($entities as $entity) {
            $entityCache->add($entity);
        }

        return $entities;
    }

    private function filterEntitiesByValidityPeriod($validityIntervalBasedEntity, Row $row): array
    {
        if (isset($this->toField) && $row->has($this->toField)) {
            return $validityIntervalBasedEntity->getAllByDateInterval(
                $row->get($this->fromField)->value(),
                $row->get($this->toField)->value(),
            );
        } else {
            return $validityIntervalBasedEntity->getByDay(
                $row->get($this->fromField)->value(),
            );
        }
    }

    private function getValuesFromRow(Row $row): array
    {
        $values = [];
        foreach ($this->fieldMap as $rowField => $entityField) {
            if ($row->has($rowField)) {
                $values[$rowField] = $row->get($rowField)->value();
            }
        }
        return $values;
    }

    private function collectIdentifiersFromRow(Row $row): array
    {
        $identifiers = [];
        foreach ($this->identifierFields as $identifierFieldName => $fieldName) {
            if (!$row->has($fieldName)) {
                throw new \Exception("Identifier field not found: {$identifierFieldName} -> {$fieldName}");
            }
            $identifiers[$identifierFieldName] = $row->get($fieldName)->value();
        }

        return $identifiers;
    }
}
