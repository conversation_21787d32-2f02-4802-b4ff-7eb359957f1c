<?php

declare(strict_types=1);

namespace LoginAutonom\CoreBundle\Enum;

enum DateRangePatternEnum: string
{
    case CURRENT_DATE = 'currentDate';
    case CURRENT_MONTH = 'currentMonth';
    case NEXT_MONTH = 'nextMonth';
    case PREV_MONTH = 'prevMonth';
    case LAST_N_MONTHS = 'lastNMonths';
    case NEXT_N_MONTHS = 'nextNMonths';
    case CUSTOM_RANGE = 'customRange';
    case DATE_PATTERN = 'datePattern';
    case MAX_MONTHS = 'maxMonths';
    case BASE_DATE = 'baseDate';
    case FROM = 'from';
    case TO = 'to';
    case CUSTOM_FROM = 'customFrom';
    case CUSTOM_TO = 'customTo';
}
