<?php

declare(strict_types=1);

namespace LoginAutonom\CoreBundle\Guesser;

use Carbon\Carbon;
use LoginAutonom\CoreBundle\DTO\DateRangeConfigDTO;
use LoginAutonom\CoreBundle\Enum\DateRangeConfigKeyEnum;
use LoginAutonom\CoreBundle\Enum\DateRangePatternEnum;
use LoginAutonom\CoreBundle\Util\DateTimeUtil;

final readonly class DateRangeGuesser
{
    public function __construct(
        private int $defaultMaxMonths = 12
    ) {
    }

    public function guess(DateRangeConfigDTO $config): array
    {
        $pattern = $config->hasDatePattern() ? $config->getDatePattern() : DateRangePatternEnum::CURRENT_MONTH;
        $maxMonths = $config->hasMaxMonths() ? $config->getMaxMonths() : $this->defaultMaxMonths;
        $baseDate = $config->hasBaseDate() ? $config->getBaseDate() : Carbon::now();

        return match ($pattern) {
            DateRangePatternEnum::CURRENT_DATE => $this->getCurrentDateRange($baseDate),
            DateRangePatternEnum::NEXT_MONTH => $this->getNextMonthRange($baseDate),
            DateRangePatternEnum::PREV_MONTH => $this->getPrevMonthRange($baseDate),
            DateRangePatternEnum::LAST_N_MONTHS => $this->getLastNMonthsRange($baseDate, $maxMonths),
            DateRangePatternEnum::NEXT_N_MONTHS => $this->getNextNMonthsRange($baseDate, $maxMonths),
            DateRangePatternEnum::CUSTOM_RANGE => $this->getCustomRange($config),
            default => $this->getCurrentMonthRange($baseDate),
        };
    }

    private function getCurrentDateRange(Carbon $baseDate): array
    {
        $startOfDay = $baseDate->copy()->startOfDay();
        $endOfDay = $baseDate->copy()->endOfDay();

        return [
            DateRangeConfigKeyEnum::FROM->value => $startOfDay->format(DateTimeUtil::DEFAULT_DATE_FORMAT),
            DateRangeConfigKeyEnum::TO->value => $endOfDay->format(DateTimeUtil::DEFAULT_DATE_FORMAT),
        ];
    }

    private function getCurrentMonthRange(Carbon $baseDate): array
    {
        $startOfMonth = $baseDate->copy()->startOfMonth();
        $endOfMonth = $baseDate->copy()->endOfMonth();

        return [
            DateRangeConfigKeyEnum::FROM->value => $startOfMonth->format(DateTimeUtil::DEFAULT_DATE_FORMAT),
            DateRangeConfigKeyEnum::TO->value => $endOfMonth->format(DateTimeUtil::DEFAULT_DATE_FORMAT),
        ];
    }

    private function getNextMonthRange(Carbon $baseDate): array
    {
        $startOfMonth = $baseDate->copy()->addMonth()->startOfMonth();
        $endOfMonth = $baseDate->copy()->addMonth()->endOfMonth();

        return [
            DateRangeConfigKeyEnum::FROM->value => $startOfMonth->format(DateTimeUtil::DEFAULT_DATE_FORMAT),
            DateRangeConfigKeyEnum::TO->value => $endOfMonth->format(DateTimeUtil::DEFAULT_DATE_FORMAT),
        ];
    }

    private function getPrevMonthRange(Carbon $baseDate): array
    {
        $startOfMonth = $baseDate->copy()->subMonth()->startOfMonth();
        $endOfMonth = $baseDate->copy()->subMonth()->endOfMonth();

        return [
            DateRangeConfigKeyEnum::FROM->value => $startOfMonth->format(DateTimeUtil::DEFAULT_DATE_FORMAT),
            DateRangeConfigKeyEnum::TO->value => $endOfMonth->format(DateTimeUtil::DEFAULT_DATE_FORMAT),
        ];
    }

    private function getLastNMonthsRange(Carbon $baseDate, int $months): array
    {
        $months = max(1, min($months, $this->defaultMaxMonths));

        $endOfRange = $baseDate->copy()->endOfMonth();
        $startOfRange = $baseDate->copy()->subMonths($months)->startOfMonth();

        return [
            DateRangeConfigKeyEnum::FROM->value => $startOfRange->format(DateTimeUtil::DEFAULT_DATE_FORMAT),
            DateRangeConfigKeyEnum::TO->value => $endOfRange->format(DateTimeUtil::DEFAULT_DATE_FORMAT),
        ];
    }

    private function getNextNMonthsRange(Carbon $baseDate, int $months): array
    {
        $months = max(1, min($months, $this->defaultMaxMonths));

        $startOfRange = $baseDate->copy()->addMonth()->startOfMonth();
        $endOfRange = $baseDate->copy()->addMonths($months)->endOfMonth();

        return [
            DateRangeConfigKeyEnum::FROM->value => $startOfRange->format(DateTimeUtil::DEFAULT_DATE_FORMAT),
            DateRangeConfigKeyEnum::TO->value => $endOfRange->format(DateTimeUtil::DEFAULT_DATE_FORMAT),
        ];
    }

    private function getCustomRange(DateRangeConfigDTO $config): array
    {
        $from = $config->getCustomFrom();
        $to = $config->getCustomTo();

        if (!$from || !$to) {
            throw new \InvalidArgumentException('Custom range requires both from and to dates');
        }

        $fromDate = Carbon::parse($from);
        $toDate = Carbon::parse($to);

        $monthsDiff = $fromDate->diffInMonths($toDate);
        if ($monthsDiff > $this->defaultMaxMonths) {
            throw new \InvalidArgumentException(
                "Custom range exceeds maximum allowed months: {$this->defaultMaxMonths}"
            );
        }

        return [
            DateRangeConfigKeyEnum::FROM->value => $fromDate->format(DateTimeUtil::DEFAULT_DATE_FORMAT),
            DateRangeConfigKeyEnum::TO->value => $toDate->format(DateTimeUtil::DEFAULT_DATE_FORMAT),
        ];
    }
}
